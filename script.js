// Runware API Configuration
const RUNWARE_API_KEY = 'yRQ2DLBEKBBPemhULO4sLXLUy4VwITMJ';
const RUNWARE_API_URL = 'https://api.runware.ai/v1';

// Global variables
let currentImageData = null;
let processingState = {
    step1: { status: 'pending', result: null, cost: 0 },
    step2: { status: 'pending', result: null, cost: 0 },
    step3: { status: 'pending', result: null, cost: 0 },
    totalCost: 0
};

// Style prompts mapping
const STYLE_PROMPTS = {
    minimalist: "clean white background, minimalist studio, soft shadows, professional product photography, studio lighting, high resolution, commercial photography, clean composition, 8k, detailed, sharp focus",
    luxury: "luxury marble surface, elegant lighting, premium feel, sophisticated ambiance, professional product photography, studio lighting, high resolution, commercial photography, clean composition, 8k, detailed, sharp focus",
    natural: "natural wooden table, soft natural lighting, organic feel, warm tones, professional product photography, studio lighting, high resolution, commercial photography, clean composition, 8k, detailed, sharp focus",
    modern: "modern concrete surface, contemporary lighting, industrial feel, clean lines, professional product photography, studio lighting, high resolution, commercial photography, clean composition, 8k, detailed, sharp focus",
    lifestyle: "cozy home setting, warm lighting, lived-in feel, comfortable atmosphere, professional product photography, studio lighting, high resolution, commercial photography, clean composition, 8k, detailed, sharp focus",
    studio: "professional photography studio, softbox lighting, seamless backdrop, professional product photography, studio lighting, high resolution, commercial photography, clean composition, 8k, detailed, sharp focus"
};

const NEGATIVE_PROMPT = "blurry, low quality, distorted, overexposed, underexposed, noisy, artifact, watermark, text, logo, cropped, out of frame, deformed, mutation";

// DOM Elements
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const imageUrl = document.getElementById('imageUrl');
const loadFromUrlBtn = document.getElementById('loadFromUrl');
const processBtn = document.getElementById('processBtn');
const previewSection = document.getElementById('previewSection');
const previewImage = document.getElementById('previewImage');
const imageInfo = document.getElementById('imageInfo');
const progressSection = document.getElementById('progressSection');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const resultsSection = document.getElementById('resultsSection');
const errorSection = document.getElementById('errorSection');
const errorMessage = document.getElementById('errorMessage');
const retryBtn = document.getElementById('retryBtn');

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    // File upload
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);

    // URL input
    loadFromUrlBtn.addEventListener('click', handleUrlLoad);
    imageUrl.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') handleUrlLoad();
    });

    // Process button
    processBtn.addEventListener('click', startProcessing);

    // Retry button
    retryBtn.addEventListener('click', resetAndRetry);
}

// File handling functions
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    // Validate file
    if (!validateFile(file)) return;

    // Convert to data URI
    const reader = new FileReader();
    reader.onload = function(e) {
        currentImageData = e.target.result;
        showPreview(currentImageData, {
            name: file.name,
            size: formatFileSize(file.size),
            type: file.type
        });
        processBtn.disabled = false;
    };
    reader.readAsDataURL(file);
}

function handleUrlLoad() {
    const url = imageUrl.value.trim();
    if (!validateUrl(url)) return;

    // Show loading
    loadFromUrlBtn.textContent = 'Đang tải...';
    loadFromUrlBtn.disabled = true;

    // Test URL accessibility
    fetch(url, { method: 'HEAD' })
        .then(response => {
            if (!response.ok) {
                throw new Error('URL không thể truy cập');
            }
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.startsWith('image/')) {
                throw new Error('URL không phải là ảnh hợp lệ');
            }

            currentImageData = url;
            showPreview(url, {
                name: 'Image from URL',
                size: 'Unknown',
                type: contentType
            });
            processBtn.disabled = false;
        })
        .catch(error => {
            showError('Lỗi tải URL: ' + error.message);
        })
        .finally(() => {
            loadFromUrlBtn.textContent = 'Tải từ URL';
            loadFromUrlBtn.disabled = false;
        });
}

function validateFile(file) {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        showError('Định dạng file không được hỗ trợ. Vui lòng chọn JPG, PNG hoặc WEBP.');
        return false;
    }

    // Check file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
        showError('File quá lớn. Vui lòng chọn file nhỏ hơn 10MB.');
        return false;
    }

    return true;
}

function validateUrl(url) {
    if (!url) {
        showError('Vui lòng nhập URL ảnh.');
        return false;
    }

    if (!url.startsWith('https://')) {
        showError('URL phải sử dụng HTTPS.');
        return false;
    }

    try {
        new URL(url);
        return true;
    } catch {
        showError('URL không hợp lệ.');
        return false;
    }
}

function showPreview(imageSrc, info) {
    previewImage.src = imageSrc;
    imageInfo.innerHTML = `
        <h4>Thông tin ảnh</h4>
        <p><strong>Tên:</strong> ${info.name}</p>
        <p><strong>Kích thước:</strong> ${info.size}</p>
        <p><strong>Định dạng:</strong> ${info.type}</p>
    `;
    previewSection.style.display = 'block';
    hideError();
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Processing functions
async function startProcessing() {
    if (!currentImageData) {
        showError('Vui lòng chọn ảnh trước khi xử lý.');
        return;
    }

    // Reset state
    resetProcessingState();

    // Show progress section
    progressSection.style.display = 'block';
    resultsSection.style.display = 'none';
    hideError();

    // Disable process button
    processBtn.disabled = true;
    processBtn.textContent = 'Đang xử lý...';

    try {
        // Step 1: Background Removal
        await executeStep1();

        // Step 2: Image Upscaling
        await executeStep2();

        // Step 3: Background Generation
        await executeStep3();

        // Show final results
        showResults();

    } catch (error) {
        console.error('Processing error:', error);
        showError('Có lỗi xảy ra trong quá trình xử lý: ' + error.message);
    } finally {
        processBtn.disabled = false;
        processBtn.textContent = '🚀 Bắt đầu xử lý';
    }
}

function resetProcessingState() {
    processingState = {
        step1: { status: 'pending', result: null, cost: 0 },
        step2: { status: 'pending', result: null, cost: 0 },
        step3: { status: 'pending', result: null, cost: 0 },
        totalCost: 0
    };

    updateProgress(0, 'Đang chuẩn bị...');
    updateStepStatus('step1', 'pending', 'Đang chờ...');
    updateStepStatus('step2', 'pending', 'Đang chờ...');
    updateStepStatus('step3', 'pending', 'Đang chờ...');
    updateCostDisplay();
}

function updateProgress(percentage, text) {
    progressFill.style.width = percentage + '%';
    progressText.textContent = text;
}

function updateStepStatus(stepId, status, statusText) {
    const step = document.getElementById(stepId);
    step.className = 'step ' + status;
    step.querySelector('.step-status').textContent = statusText;
}

function updateCostDisplay() {
    document.getElementById('cost1').textContent = '$' + processingState.step1.cost.toFixed(4);
    document.getElementById('cost2').textContent = '$' + processingState.step2.cost.toFixed(4);
    document.getElementById('cost3').textContent = '$' + processingState.step3.cost.toFixed(4);
    document.getElementById('totalCost').textContent = '$' + processingState.totalCost.toFixed(4);
}

// API call functions
async function executeStep1() {
    updateProgress(10, 'Bước 1: Đang xóa background...');
    updateStepStatus('step1', 'active', 'Đang xử lý...');

    const payload = [{
        taskType: "imageBackgroundRemoval",
        taskUUID: generateUUID(),
        inputImage: currentImageData,
        outputType: "URL",
        outputFormat: "PNG",
        model: "runware:112@5", // BiRefNet General
        includeCost: true
    }];

    const response = await callRunwareAPI(payload);
    const result = response.data[0];

    processingState.step1.status = 'completed';
    processingState.step1.result = result.imageURL;
    processingState.step1.cost = result.cost || 0;
    processingState.totalCost += processingState.step1.cost;

    updateProgress(30, 'Bước 1 hoàn thành!');
    updateStepStatus('step1', 'completed', 'Hoàn thành ✓');
    updateCostDisplay();

    // Show step 1 result
    showStepResult('result1', 'resultImage1', result.imageURL);

    // Wait a bit before next step
    await sleep(1000);
}

async function executeStep2() {
    updateProgress(40, 'Bước 2: Đang nâng cấp chất lượng...');
    updateStepStatus('step2', 'active', 'Đang xử lý...');

    const upscaleFactor = parseInt(document.getElementById('upscaleFactor').value);

    const payload = [{
        taskType: "imageUpscale",
        taskUUID: generateUUID(),
        inputImage: processingState.step1.result,
        outputType: "URL",
        outputFormat: "PNG",
        upscaleFactor: upscaleFactor,
        includeCost: true
    }];

    const response = await callRunwareAPI(payload);
    const result = response.data[0];

    processingState.step2.status = 'completed';
    processingState.step2.result = result.imageURL;
    processingState.step2.cost = result.cost || 0;
    processingState.totalCost += processingState.step2.cost;

    updateProgress(60, 'Bước 2 hoàn thành!');
    updateStepStatus('step2', 'completed', 'Hoàn thành ✓');
    updateCostDisplay();

    // Show step 2 result
    showStepResult('result2', 'resultImage2', result.imageURL);

    // Wait a bit before next step
    await sleep(1000);
}

async function executeStep3() {
    updateProgress(70, 'Bước 3: Đang tạo background mới...');
    updateStepStatus('step3', 'active', 'Đang xử lý...');

    const backgroundStyle = document.getElementById('backgroundStyle').value;
    const generateCount = parseInt(document.getElementById('generateCount').value);
    const positivePrompt = STYLE_PROMPTS[backgroundStyle];

    const payload = [{
        taskType: "imageInference",
        taskUUID: generateUUID(),
        positivePrompt: positivePrompt,
        negativePrompt: NEGATIVE_PROMPT,
        seedImage: processingState.step2.result,
        strength: 0.35,
        model: "runware:100@1", // SDXL model
        height: 1024,
        width: 1024,
        steps: 30,
        CFGScale: 7.5,
        numberResults: generateCount,
        controlNet: [{
            model: "runware:20@1", // SDXL Canny ControlNet
            guideImage: processingState.step2.result,
            weight: 0.8,
            startStep: 0,
            endStep: 20
        }],
        outputType: "URL",
        outputFormat: "JPG",
        outputQuality: 95,
        includeCost: true
    }];

    const response = await callRunwareAPI(payload);

    processingState.step3.status = 'completed';
    processingState.step3.result = response.data;
    processingState.step3.cost = response.data.reduce((sum, item) => sum + (item.cost || 0), 0);
    processingState.totalCost += processingState.step3.cost;

    updateProgress(100, 'Tất cả các bước đã hoàn thành!');
    updateStepStatus('step3', 'completed', 'Hoàn thành ✓');
    updateCostDisplay();
}

// Demo mode flag - set to true to use mock data for testing
const DEMO_MODE = true;

async function callRunwareAPI(payload) {
    if (DEMO_MODE) {
        // Mock API response for demo
        console.log('🎭 Demo mode: Using mock data');
        await sleep(2000); // Simulate API delay

        const taskType = payload[0].taskType;
        const taskUUID = payload[0].taskUUID;

        if (taskType === 'imageBackgroundRemoval') {
            return {
                data: [{
                    taskType: taskType,
                    taskUUID: taskUUID,
                    imageUUID: 'mock-uuid-1',
                    imageURL: 'https://via.placeholder.com/512x512/ffffff/000000?text=No+Background',
                    cost: 0.006
                }]
            };
        } else if (taskType === 'imageUpscale') {
            return {
                data: [{
                    taskType: taskType,
                    taskUUID: taskUUID,
                    imageUUID: 'mock-uuid-2',
                    imageURL: 'https://via.placeholder.com/1024x1024/f0f0f0/333333?text=Enhanced+Quality',
                    cost: 0.003
                }]
            };
        } else if (taskType === 'imageInference') {
            const count = payload[0].numberResults || 4;
            const results = [];
            for (let i = 0; i < count; i++) {
                results.push({
                    taskType: taskType,
                    taskUUID: taskUUID + '-' + i,
                    imageUUID: 'mock-uuid-3-' + i,
                    imageURL: `https://via.placeholder.com/1024x1024/e0e0e0/666666?text=Background+${i+1}`,
                    cost: 0.008
                });
            }
            return { data: results };
        }
    }

    try {
        const response = await fetch(RUNWARE_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${RUNWARE_API_KEY}`
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
            throw new Error('Không thể kết nối đến Runware API. Vui lòng kiểm tra kết nối internet hoặc thử lại sau.');
        }
        throw error;
    }
}

// Utility functions
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function showStepResult(resultId, imageId, imageUrl) {
    document.getElementById(resultId).style.display = 'block';
    document.getElementById(imageId).src = imageUrl;
    resultsSection.style.display = 'block';
}

function showResults() {
    // Show gallery with final results
    const galleryGrid = document.getElementById('galleryGrid');
    galleryGrid.innerHTML = '';

    processingState.step3.result.forEach((result, index) => {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item';
        galleryItem.innerHTML = `
            <img src="${result.imageURL}" alt="Background variant ${index + 1}" onclick="openImageModal('${result.imageURL}')" style="cursor: pointer;">
            <button class="download-btn" onclick="downloadImage('${result.imageURL}', 'product-bg-${index + 1}.jpg')">
                📥 Tải xuống
            </button>
        `;
        galleryGrid.appendChild(galleryItem);
    });

    document.getElementById('downloadAllBtn').style.display = 'block';
    resultsSection.style.display = 'block';
}

function showError(message) {
    errorMessage.textContent = message;
    errorSection.style.display = 'block';
}

function hideError() {
    errorSection.style.display = 'none';
}

function resetAndRetry() {
    hideError();
    progressSection.style.display = 'none';
    resultsSection.style.display = 'none';
    if (currentImageData) {
        processBtn.disabled = false;
    }
}

// Download functions
function downloadImage(imageUrl, filename) {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function downloadImageById(imageId, filename) {
    const img = document.getElementById(imageId);
    if (img && img.src) {
        downloadImage(img.src, filename);
    }
}

// Download all functionality
document.getElementById('downloadAllBtn').addEventListener('click', downloadAllImages);

async function downloadAllImages() {
    const downloadBtn = document.getElementById('downloadAllBtn');
    const originalText = downloadBtn.textContent;
    downloadBtn.textContent = 'Đang tải xuống...';
    downloadBtn.disabled = true;

    try {
        // Download step results
        if (processingState.step1.result) {
            await downloadImageFromUrl(processingState.step1.result, 'product-no-background.png');
        }

        if (processingState.step2.result) {
            await downloadImageFromUrl(processingState.step2.result, 'product-enhanced.png');
        }

        // Download all background variants
        if (processingState.step3.result) {
            for (let i = 0; i < processingState.step3.result.length; i++) {
                const result = processingState.step3.result[i];
                await downloadImageFromUrl(result.imageURL, `product-background-${i + 1}.jpg`);
                await sleep(500); // Small delay between downloads
            }
        }

        alert('Tất cả ảnh đã được tải xuống thành công!');
    } catch (error) {
        console.error('Download error:', error);
        alert('Có lỗi xảy ra khi tải xuống: ' + error.message);
    } finally {
        downloadBtn.textContent = originalText;
        downloadBtn.disabled = false;
    }
}

async function downloadImageFromUrl(imageUrl, filename) {
    try {
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Error downloading image:', error);
        // Fallback to direct link
        downloadImage(imageUrl, filename);
    }
}

// Enhanced error handling for API calls
async function callRunwareAPIWithRetry(payload, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await callRunwareAPI(payload);
        } catch (error) {
            console.error(`API call attempt ${attempt} failed:`, error);

            if (attempt === maxRetries) {
                throw error;
            }

            // Exponential backoff: 2^attempt seconds
            const waitTime = Math.pow(2, attempt) * 1000;
            updateProgress(
                Math.min(90, (attempt / maxRetries) * 100),
                `Thử lại lần ${attempt}... Đợi ${waitTime/1000}s`
            );
            await sleep(waitTime);
        }
    }
}

// Update API calls to use retry mechanism
async function executeStep1() {
    updateProgress(10, 'Bước 1: Đang xóa background...');
    updateStepStatus('step1', 'active', 'Đang xử lý...');

    const payload = [{
        taskType: "imageBackgroundRemoval",
        taskUUID: generateUUID(),
        inputImage: currentImageData,
        outputType: "URL",
        outputFormat: "PNG",
        model: "runware:112@5", // BiRefNet General
        includeCost: true
    }];

    const response = await callRunwareAPIWithRetry(payload);
    const result = response.data[0];

    processingState.step1.status = 'completed';
    processingState.step1.result = result.imageURL;
    processingState.step1.cost = result.cost || 0;
    processingState.totalCost += processingState.step1.cost;

    updateProgress(30, 'Bước 1 hoàn thành!');
    updateStepStatus('step1', 'completed', 'Hoàn thành ✓');
    updateCostDisplay();

    // Show step 1 result
    showStepResult('result1', 'resultImage1', result.imageURL);

    // Wait a bit before next step
    await sleep(1000);
}

async function executeStep2() {
    updateProgress(40, 'Bước 2: Đang nâng cấp chất lượng...');
    updateStepStatus('step2', 'active', 'Đang xử lý...');

    const upscaleFactor = parseInt(document.getElementById('upscaleFactor').value);

    const payload = [{
        taskType: "imageUpscale",
        taskUUID: generateUUID(),
        inputImage: processingState.step1.result,
        outputType: "URL",
        outputFormat: "PNG",
        upscaleFactor: upscaleFactor,
        includeCost: true
    }];

    const response = await callRunwareAPIWithRetry(payload);
    const result = response.data[0];

    processingState.step2.status = 'completed';
    processingState.step2.result = result.imageURL;
    processingState.step2.cost = result.cost || 0;
    processingState.totalCost += processingState.step2.cost;

    updateProgress(60, 'Bước 2 hoàn thành!');
    updateStepStatus('step2', 'completed', 'Hoàn thành ✓');
    updateCostDisplay();

    // Show step 2 result
    showStepResult('result2', 'resultImage2', result.imageURL);

    // Wait a bit before next step
    await sleep(1000);
}

async function executeStep3() {
    updateProgress(70, 'Bước 3: Đang tạo background mới...');
    updateStepStatus('step3', 'active', 'Đang xử lý...');

    const backgroundStyle = document.getElementById('backgroundStyle').value;
    const generateCount = parseInt(document.getElementById('generateCount').value);
    const positivePrompt = STYLE_PROMPTS[backgroundStyle];

    const payload = [{
        taskType: "imageInference",
        taskUUID: generateUUID(),
        positivePrompt: positivePrompt,
        negativePrompt: NEGATIVE_PROMPT,
        seedImage: processingState.step2.result,
        strength: 0.35,
        model: "runware:100@1", // SDXL model
        height: 1024,
        width: 1024,
        steps: 30,
        CFGScale: 7.5,
        numberResults: generateCount,
        controlNet: [{
            model: "runware:20@1", // SDXL Canny ControlNet
            guideImage: processingState.step2.result,
            weight: 0.8,
            startStep: 0,
            endStep: 20
        }],
        outputType: "URL",
        outputFormat: "JPG",
        outputQuality: 95,
        includeCost: true
    }];

    const response = await callRunwareAPIWithRetry(payload);

    processingState.step3.status = 'completed';
    processingState.step3.result = response.data;
    processingState.step3.cost = response.data.reduce((sum, item) => sum + (item.cost || 0), 0);
    processingState.totalCost += processingState.step3.cost;

    updateProgress(100, 'Tất cả các bước đã hoàn thành!');
    updateStepStatus('step3', 'completed', 'Hoàn thành ✓');
    updateCostDisplay();
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + U to upload
    if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
        e.preventDefault();
        fileInput.click();
    }

    // Enter to start processing (if image is loaded)
    if (e.key === 'Enter' && currentImageData && !processBtn.disabled) {
        e.preventDefault();
        startProcessing();
    }

    // Escape to reset
    if (e.key === 'Escape') {
        e.preventDefault();
        resetAndRetry();
    }
});

// Add loading states and better UX
function setLoadingState(isLoading) {
    const elements = [
        document.getElementById('backgroundStyle'),
        document.getElementById('upscaleFactor'),
        document.getElementById('generateCount'),
        fileInput,
        loadFromUrlBtn,
        imageUrl
    ];

    elements.forEach(el => {
        if (el) el.disabled = isLoading;
    });
}

// Update processing functions to use loading states
async function startProcessing() {
    if (!currentImageData) {
        showError('Vui lòng chọn ảnh trước khi xử lý.');
        return;
    }

    // Set loading state
    setLoadingState(true);

    // Reset state
    resetProcessingState();

    // Show progress section
    progressSection.style.display = 'block';
    resultsSection.style.display = 'none';
    hideError();

    // Disable process button
    processBtn.disabled = true;
    processBtn.textContent = 'Đang xử lý...';

    try {
        // Step 1: Background Removal
        await executeStep1();

        // Step 2: Image Upscaling
        await executeStep2();

        // Step 3: Background Generation
        await executeStep3();

        // Show final results
        showResults();

    } catch (error) {
        console.error('Processing error:', error);
        showError('Có lỗi xảy ra trong quá trình xử lý: ' + error.message);

        // Mark current step as error
        const currentStep = getCurrentStep();
        if (currentStep) {
            updateStepStatus(currentStep, 'error', 'Lỗi ❌');
        }
    } finally {
        processBtn.disabled = false;
        processBtn.textContent = '🚀 Bắt đầu xử lý';
        setLoadingState(false);
    }
}

function getCurrentStep() {
    if (processingState.step1.status === 'pending') return 'step1';
    if (processingState.step2.status === 'pending') return 'step2';
    if (processingState.step3.status === 'pending') return 'step3';
    return null;
}

// Add image preview modal functionality
function openImageModal(imageSrc) {
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.innerHTML = `
        <div class="modal-backdrop" onclick="closeImageModal()">
            <div class="modal-content" onclick="event.stopPropagation()">
                <img src="${imageSrc}" alt="Full size image">
                <button class="modal-close" onclick="closeImageModal()">✕</button>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    const modal = document.querySelector('.image-modal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
    }
}

// Add modal styles to CSS (will be added via JavaScript)
const modalStyles = `
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal-backdrop {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    cursor: default;
}

.modal-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 10px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.modal-close {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: #f0f0f0;
}
`;

// Add modal styles to document
const styleSheet = document.createElement('style');
styleSheet.textContent = modalStyles;
document.head.appendChild(styleSheet);

console.log('🎨 Runware Product Photography - Website loaded successfully!');
