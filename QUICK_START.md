# 🚀 Quick Start Guide

## Chạy ngay lập tức (Demo Mode)

1. **Mở website:**
   ```bash
   python -m http.server 8000
   ```
   Sau đó mở: http://localhost:8000

2. **Test website:**
   - Upload ảnh bất kỳ (hoặc nhập URL ảnh)
   - Chọn cài đặt phù hợp
   - Click "🚀 Bắt đầu xử lý"
   - Xem kết quả demo với mock data

## Chuyển sang Real API

1. **Đổi mode trong script.js:**
   ```javascript
   const DEMO_MODE = false; // Đổi từ true sang false
   ```

2. **G<PERSON><PERSON><PERSON> quyết CORS (chọn 1 cách):**

   **Cách A: Sử dụng Proxy Server**
   ```bash
   npm install
   npm start
   # Mở http://localhost:3000
   ```

   **Cách B: Disable CORS trong Chrome**
   ```bash
   chrome --disable-web-security --user-data-dir=/tmp/chrome_dev
   ```

   **Cách C: Deploy lên server HTTPS**

## Cấu trúc Files

```
product-background/
├── index.html          # Giao diện chính
├── style.css           # Styling
├── script.js           # Logic (đổi DEMO_MODE ở đây)
├── proxy-server.js     # Proxy để bypass CORS
├── package.json        # Dependencies cho proxy
├── README.md           # Hướng dẫn chi tiết
└── QUICK_START.md      # File này
```

## Troubleshooting

### ❌ Lỗi CORS
**Triệu chứng:** Console hiển thị "CORS policy" error

**Giải pháp:**
1. Sử dụng proxy server (khuyến nghị)
2. Hoặc chạy Chrome với --disable-web-security
3. Hoặc deploy lên server có HTTPS

### ❌ Lỗi "Failed to fetch"
**Triệu chứng:** Không kết nối được API

**Giải pháp:**
1. Kiểm tra internet connection
2. Kiểm tra API key trong script.js
3. Thử chuyển về DEMO_MODE = true để test

### ❌ Ảnh không hiển thị
**Triệu chứng:** Upload thành công nhưng không thấy preview

**Giải pháp:**
1. Kiểm tra format ảnh (JPG, PNG, WEBP)
2. Kiểm tra size ảnh (< 10MB)
3. Thử ảnh khác

## Demo Features

Trong Demo Mode, website sẽ:
- ✅ Hiển thị đầy đủ UI/UX
- ✅ Simulate API calls với delay thực tế
- ✅ Show progress bars và cost tracking
- ✅ Generate mock results với placeholder images
- ✅ Test tất cả chức năng download

## Real API Features

Khi chuyển sang Real API:
- 🎯 Xóa background thật với BiRefNet
- ✨ Upscale ảnh thật với AI
- 🎨 Tạo background mới với SDXL + ControlNet
- 💰 Cost tracking thật từ Runware
- 📥 Download ảnh thật chất lượng cao

## API Cost Estimate

- Background Removal: ~$0.006
- Upscaling: ~$0.003  
- Background Generation: ~$0.008 x số variants
- **Total**: ~$0.017 - $0.065 per job

## Support

Nếu gặp vấn đề:
1. Check browser console (F12)
2. Thử DEMO_MODE = true trước
3. Đọc README.md để biết thêm chi tiết
4. Kiểm tra network connection

---
**Happy coding! 🎨**
