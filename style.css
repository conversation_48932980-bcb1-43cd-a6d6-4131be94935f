/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
main {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

/* Input Section */
.input-section {
    margin-bottom: 40px;
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.upload-area:hover {
    border-color: #764ba2;
    background: #f0f2ff;
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #4CAF50;
    background: #e8f5e8;
}

.upload-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.upload-content h3 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.file-info {
    color: #666;
    font-size: 0.9rem;
    margin-top: 10px;
}

/* URL Input */
.url-input-section {
    margin: 30px 0;
}

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #ddd;
}

.divider span {
    background: white;
    padding: 0 20px;
    color: #666;
    font-weight: bold;
}

.url-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.url-input input {
    flex: 1;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.url-input input:focus {
    outline: none;
    border-color: #667eea;
}

.url-input button {
    padding: 15px 25px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.3s ease;
}

.url-input button:hover {
    background: #5a6fd8;
}

/* Settings */
.settings-section {
    margin: 30px 0;
    padding: 25px;
    background: #f8f9ff;
    border-radius: 15px;
}

.settings-section h3 {
    color: #667eea;
    margin-bottom: 20px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-item label {
    font-weight: bold;
    color: #333;
}

.setting-item select {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
}

.setting-item select:focus {
    outline: none;
    border-color: #667eea;
}

/* Process Button */
.process-btn {
    width: 100%;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 30px;
}

.process-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.process-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Preview Section */
.preview-section {
    margin: 40px 0;
    padding: 25px;
    background: #f8f9ff;
    border-radius: 15px;
}

.preview-section h3 {
    color: #667eea;
    margin-bottom: 20px;
}

.preview-container {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.preview-container img {
    max-width: 300px;
    max-height: 300px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.image-info {
    flex: 1;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

/* Progress Section */
.progress-section {
    margin: 40px 0;
    padding: 25px;
    background: #f8f9ff;
    border-radius: 15px;
}

.progress-section h3 {
    color: #667eea;
    margin-bottom: 20px;
}

.progress-container {
    margin-bottom: 30px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    font-weight: bold;
    color: #667eea;
}

/* Steps */
.steps-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.step {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.step.active {
    border-left: 5px solid #667eea;
    transform: translateX(5px);
}

.step.completed {
    border-left: 5px solid #4CAF50;
    background: #f0fff0;
}

.step.error {
    border-left: 5px solid #f44336;
    background: #fff0f0;
}

.step-icon {
    font-size: 2rem;
    width: 60px;
    text-align: center;
}

.step-content {
    flex: 1;
}

.step-content h4 {
    color: #333;
    margin-bottom: 5px;
}

.step-content p {
    color: #666;
    margin-bottom: 10px;
}

.step-status {
    font-weight: bold;
    color: #667eea;
}

.step.active .step-status {
    color: #667eea;
}

.step.completed .step-status {
    color: #4CAF50;
}

.step.error .step-status {
    color: #f44336;
}

/* Cost Tracking */
.cost-container {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.cost-container h4 {
    color: #667eea;
    margin-bottom: 15px;
}

.cost-breakdown {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.cost-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.cost-total {
    display: flex;
    justify-content: space-between;
    padding: 15px 0 5px 0;
    font-weight: bold;
    font-size: 1.1rem;
    color: #667eea;
    border-top: 2px solid #667eea;
    margin-top: 10px;
}

/* Results Section */
.results-section {
    margin: 40px 0;
}

.results-section h3 {
    color: #667eea;
    margin-bottom: 30px;
    text-align: center;
    font-size: 2rem;
}

.step-results {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.step-result {
    background: #f8f9ff;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
}

.step-result h4 {
    color: #667eea;
    margin-bottom: 20px;
}

.result-image-container {
    position: relative;
    display: inline-block;
}

.result-image-container img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.download-btn {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(102, 126, 234, 0.9);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.3s ease;
}

.download-btn:hover {
    background: rgba(102, 126, 234, 1);
}

/* Gallery */
.gallery h4 {
    color: #667eea;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5rem;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.gallery-item {
    background: #f8f9ff;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-item img {
    width: 100%;
    max-height: 250px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.download-all-btn {
    display: block;
    margin: 0 auto;
    padding: 15px 30px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.download-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
}

/* Error Section */
.error-section {
    margin: 40px 0;
    text-align: center;
}

.error-container {
    background: #fff0f0;
    padding: 40px;
    border-radius: 15px;
    border: 2px solid #f44336;
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.error-container h3 {
    color: #f44336;
    margin-bottom: 15px;
}

.error-container p {
    color: #666;
    margin-bottom: 25px;
    font-size: 1.1rem;
}

.retry-btn {
    padding: 15px 30px;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s ease;
}

.retry-btn:hover {
    background: #d32f2f;
}

/* Footer */
footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    margin-top: 20px;
}

footer a {
    color: white;
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    main {
        padding: 20px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .upload-area {
        padding: 20px;
    }
    
    .upload-icon {
        font-size: 3rem;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .preview-container {
        flex-direction: column;
    }
    
    .steps-container {
        gap: 15px;
    }
    
    .step {
        padding: 15px;
    }
    
    .step-results {
        grid-template-columns: 1fr;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
    }
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}
