# 🧠 Runware Product Photography - Logic-Tight Development Scratchpad

## 📋 Critical Implementation Logic & Data Flow

### **1. INPUT HANDLING - Image Acquisition Methods**

#### **Method A: File Upload Processing**
```
User uploads file → File validation → Convert to appropriate format for Runware
```

**Validation Requirements:**
- File size: Maximum 10MB (10,485,760 bytes)
- Supported formats: JPG, JPEG, PNG, WEBP only
- MIME type validation: image/jpeg, image/png, image/webp
- File extension validation: .jpg, .jpeg, .png, .webp

**Conversion Logic:**
```
Local File → Read as Buffer → Convert to Base64 → Create Data URI
```

**Data URI Format (MANDATORY):**
- Structure: `data:<MIME-TYPE>;base64,<BASE64-DATA>`
- Example: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...`
- MIME types: image/jpeg, image/png, image/webp
- Size limit: 5MB for Data URI (base64 increases size by ~33%)

#### **Method B: URL Input Processing**
```
User provides URL → URL validation → Format verification → Accessibility check
```

**URL Requirements (STRICT):**
- Must be HTTPS only (HTTP rejected)
- Must be publicly accessible (no authentication)
- Must return proper Content-Type header
- Must support HTTP HEAD requests
- URL length maximum 2048 characters
- No redirects followed (3XX = failure)
- Must be domain-based (not IP addresses)

**URL Validation Process:**
1. Check HTTPS protocol
2. Validate URL format
3. Test accessibility with HEAD request
4. Verify Content-Type header matches image/*
5. Check Content-Length if available

#### **Method C: Image Upload to Runware (Optional)**
```
Image Data → Upload to Runware → Get UUID → Use UUID in subsequent tasks
```

**Upload Task Structure:**
```json
{
  "taskType": "imageUpload",
  "taskUUID": "<UNIQUE-UUID-V4>",
  "image": "<DATA-URI-FORMAT>"
}
```

**Response Format:**
```json
{
  "data": {
    "taskType": "imageUpload", 
    "taskUUID": "<SAME-UUID>",
    "imageUUID": "<GENERATED-UUID>"
  }
}
```

### **2. RUNWARE API INTEGRATION - Request/Response Logic**

#### **Universal Request Structure (MANDATORY)**
```json
[
  {
    "taskType": "<TASK-TYPE>",
    "taskUUID": "<UNIQUE-UUID-V4>", 
    "inputImage": "<IMAGE-INPUT>",
    "outputType": "URL",
    "outputFormat": "<FORMAT>",
    "includeCost": true,
    "<TASK-SPECIFIC-PARAMS>": "..."
  }
]
```

**Critical Rules:**
- Always send as ARRAY (even single task)
- taskUUID must be unique UUID v4 for each task
- inputImage can be: UUID string, Data URI, Base64, or Public URL
- outputType: "URL" (recommended), "base64Data", or "dataURI"
- Response will contain same taskUUID for matching

#### **Universal Response Structure**
```json
{
  "data": [
    {
      "taskType": "<TASK-TYPE>",
      "taskUUID": "<SAME-UUID>", 
      "imageUUID": "<GENERATED-UUID>",
      "imageURL": "<RESULT-URL>",
      "cost": 0.006
    }
  ]
}
```

### **3. PROCESSING PIPELINE - Step-by-Step Execution**

#### **Step 1: Background Removal**
```
Input Image → Runware Background Removal → Cutout Image URL
```

**Request Payload:**
```json
{
  "taskType": "imageBackgroundRemoval",
  "taskUUID": "<UUID>",
  "inputImage": "<INPUT-DATA>",
  "outputType": "URL",
  "outputFormat": "PNG",
  "model": "runware:112@5",
  "settings": {
    "rgba": [255, 255, 255, 0],
    "postProcessMask": true,
    "returnOnlyMask": false,
    "alphaMatting": true,
    "alphaMattingForegroundThreshold": 240,
    "alphaMattingBackgroundThreshold": 10,
    "alphaMattingErodeSize": 10
  },
  "includeCost": true
}
```

**Critical Notes:**
- MUST use PNG format to preserve transparency
- model "runware:112@5" (BiRefNet General) for best quality
- alphaMatting=true for smooth edges
- RGBA [255,255,255,0] for transparent background

**Result Processing:**
- Extract imageURL from response.data[0].imageURL
- Store cost from response.data[0].cost
- Use imageURL as input for next step

#### **Step 2: Image Enhancement/Upscaling**
```
Cutout Image URL → Runware Upscaling → Enhanced Image URL
```

**Request Payload:**
```json
{
  "taskType": "imageUpscale",
  "taskUUID": "<NEW-UUID>",
  "inputImage": "<CUTOUT-IMAGE-URL>",
  "outputType": "URL", 
  "outputFormat": "PNG",
  "upscaleFactor": 2,
  "outputQuality": 95,
  "includeCost": true
}
```

**Upscale Factor Logic:**
- 1: No upscaling (fastest)
- 2: 2x size (recommended balance)
- 3: 3x size (high quality)
- 4: 4x size (maximum, slowest)

**Size Limitations:**
- Maximum result: 4096x4096 pixels
- If input_size × upscale_factor > 4096, image auto-resized
- Example: 2048×2048 × 4 = auto-resize to 1024×1024 first

#### **Step 3: Background Generation**
```
Enhanced Image URL → Runware Image Inference → Multiple Background Variants
```

**Request Payload:**
```json
{
  "taskType": "imageInference", 
  "taskUUID": "<NEW-UUID>",
  "positivePrompt": "<STYLE-PROMPT>",
  "negativePrompt": "<NEGATIVE-PROMPT>",
  "initImage": "<ENHANCED-IMAGE-URL>",
  "strength": 0.35,
  "model": "runware:100@1",
  "height": 1024,
  "width": 1024,
  "steps": 30,
  "CFGScale": 7.5,
  "numberResults": 4,
  "controlNet": [
    {
      "model": "runware:101@1",
      "guideImage": "<ENHANCED-IMAGE-URL>",
      "weight": 0.8,
      "startStep": 0,
      "endStep": 20
    }
  ],
  "lora": [
    {
      "model": "runware:120@2",
      "weight": 0.8
    }
  ],
  "outputType": "URL",
  "outputFormat": "JPG",
  "outputQuality": 95,
  "includeCost": true
}
```

**Prompt Engineering:**
```
Positive: "<STYLE-SPECIFIC-PROMPT>, professional product photography, studio lighting, high resolution, commercial photography, clean composition, 8k, detailed, sharp focus"

Negative: "blurry, low quality, distorted, overexposed, underexposed, noisy, artifact, watermark, text, logo, cropped, out of frame, deformed, mutation"
```

**Style-Specific Prompts:**
- **Minimalist**: "clean white background, minimalist studio, soft shadows"
- **Luxury**: "luxury marble surface, elegant lighting, premium feel, sophisticated ambiance"
- **Natural**: "natural wooden table, soft natural lighting, organic feel, warm tones"
- **Modern**: "modern concrete surface, contemporary lighting, industrial feel, clean lines"
- **Lifestyle**: "cozy home setting, warm lighting, lived-in feel, comfortable atmosphere"
- **Outdoor**: "natural outdoor setting, soft daylight, fresh environment, scenic background"
- **Studio**: "professional photography studio, softbox lighting, seamless backdrop"
- **Abstract**: "abstract geometric background, modern design, artistic composition"

**ControlNet Configuration:**
- Model: "runware:101@1" (Depth ControlNet)
- Weight: 0.8 (strong guidance)
- Guide image: Same enhanced image URL
- Start/End steps: 0-20 (early guidance)

**LoRA Configuration:**
- Model: "runware:120@2" (Product Photography LoRA)
- Weight: 0.8 (strong effect)

### **4. RESPONSE HANDLING - Data Extraction Logic**

#### **Single Task Response Processing:**
```javascript
function processResponse(response, taskUUID) {
  // Validate response structure
  if (!response.data || !Array.isArray(response.data)) {
    throw new Error("Invalid response format");
  }
  
  // Find matching task by UUID
  const taskResult = response.data.find(item => item.taskUUID === taskUUID);
  if (!taskResult) {
    throw new Error("Task UUID not found in response");
  }
  
  // Extract result data
  const result = {
    imageURL: taskResult.imageURL,
    imageUUID: taskResult.imageUUID,
    cost: taskResult.cost || 0,
    taskType: taskResult.taskType
  };
  
  return result;
}
```

#### **Multi-Task Response Processing:**
```javascript
function processBatchResponse(response) {
  const results = [];
  
  response.data.forEach(taskResult => {
    if (taskResult.imageURL) {
      results.push({
        imageURL: taskResult.imageURL,
        imageUUID: taskResult.imageUUID,
        cost: taskResult.cost || 0,
        taskUUID: taskResult.taskUUID
      });
    }
  });
  
  return results;
}
```

### **5. ERROR HANDLING - Failure Recovery Logic**

#### **Error Categories & Responses:**

**A. Input Validation Errors (Client-side)**
- Invalid file type → "Please upload JPG, PNG, or WEBP files"
- File too large → "File must be under 10MB"
- Invalid URL → "Please provide a valid HTTPS image URL"
- Missing API key → "Please enter your Runware API key"

**B. API Authentication Errors**
```json
{
  "error": {
    "code": "INVALID_API_KEY",
    "message": "Invalid API key"
  }
}
```
Response: "Please check your Runware API key"

**C. Processing Errors**
```json
{
  "error": {
    "code": "INSUFFICIENT_CREDITS", 
    "message": "Insufficient credits"
  }
}
```
Response: "Please add credits to your Runware account"

**D. Network/Timeout Errors**
- Connection timeout → Retry with exponential backoff
- 429 Rate limiting → Wait and retry
- 5XX Server errors → Retry up to 3 times

#### **Retry Logic Implementation:**
```javascript
async function apiCallWithRetry(payload, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await makeRunwareAPICall(payload);
      return response;
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Exponential backoff: 2^attempt seconds
      const waitTime = Math.pow(2, attempt) * 1000;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
}
```

### **6. STATE MANAGEMENT - Job Tracking System**

#### **Job State Structure:**
```javascript
const jobState = {
  jobId: "uuid-v4",
  status: "processing", // "pending", "processing", "completed", "failed"
  steps: [
    {
      name: "Background Removal",
      status: "completed", // "pending", "processing", "completed", "failed"
      result: "https://im.runware.ai/image/...",
      cost: 0.006,
      taskUUID: "uuid-v4"
    },
    {
      name: "Image Enhancement", 
      status: "processing",
      result: null,
      cost: 0,
      taskUUID: "uuid-v4"
    },
    {
      name: "Background Generation",
      status: "pending",
      result: [],
      cost: 0,
      taskUUID: "uuid-v4"
    }
  ],
  totalCost: 0.006,
  startTime: 1640995200000,
  endTime: null,
  input: {
    type: "file", // "file" or "url"
    source: "original-filename.jpg"
  },
  settings: {
    backgroundStyle: "minimalist",
    upscaleFactor: 2,
    generateCount: 4,
    finalPolish: false
  }
};
```

#### **Progress Calculation:**
```javascript
function calculateProgress(jobState) {
  const stepWeights = {
    "Background Removal": 30,    // 30%
    "Image Enhancement": 20,     // 20% 
    "Background Generation": 50  // 50%
  };
  
  let totalProgress = 0;
  
  jobState.steps.forEach(step => {
    const weight = stepWeights[step.name] || 0;
    
    if (step.status === "completed") {
      totalProgress += weight;
    } else if (step.status === "processing") {
      totalProgress += weight * 0.5; // 50% of step weight
    }
  });
  
  return Math.min(totalProgress, 100);
}
```

### **7. IMAGE URL MANAGEMENT - Link Handling Strategy**

#### **URL Lifecycle Management:**
```
Runware URLs → Temporary storage (24h) → Optional permanent storage
```

**Runware URL Characteristics:**
- Format: `https://im.runware.ai/image/...`
- Expiration: 24-48 hours typically
- Direct downloadable
- No authentication required

#### **URL Processing Pipeline:**
```javascript
async function processImageURL(runwareURL, filename) {
  // 1. Validate URL accessibility
  const isAccessible = await checkURLAccessibility(runwareURL);
  if (!isAccessible) {
    throw new Error("Generated image URL is not accessible");
  }
  
  // 2. Generate metadata
  const metadata = {
    originalURL: runwareURL,
    filename: filename,
    timestamp: Date.now(),
    downloaded: false
  };
  
  // 3. Optional: Download and store locally
  if (ENABLE_LOCAL_STORAGE) {
    const localPath = await downloadAndStore(runwareURL, filename);
    metadata.localPath = localPath;
    metadata.downloaded = true;
  }
  
  return metadata;
}
```

#### **Download Implementation:**
```javascript
async function downloadImage(imageURL, filename) {
  try {
    const response = await fetch(imageURL);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const blob = await response.blob();
    
    // Create download link
    const downloadURL = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadURL;
    link.download = filename;
    link.click();
    
    // Cleanup
    URL.revokeObjectURL(downloadURL);
    
    return true;
  } catch (error) {
    console.error("Download failed:", error);
    return false;
  }
}
```

### **8. COST TRACKING - Financial Management**

#### **Cost Accumulation Logic:**
```javascript
class CostTracker {
  constructor() {
    this.costs = {
      backgroundRemoval: 0,
      imageUpscaling: 0, 
      backgroundGeneration: 0,
      total: 0
    };
  }
  
  addCost(taskType, amount) {
    switch(taskType) {
      case "imageBackgroundRemoval":
        this.costs.backgroundRemoval += amount;
        break;
      case "imageUpscale":
        this.costs.imageUpscaling += amount;
        break;
      case "imageInference":
        this.costs.backgroundGeneration += amount;
        break;
    }
    
    this.costs.total = Object.values(this.costs)
      .filter(cost => typeof cost === 'number')
      .reduce((sum, cost) => sum + cost, 0);
  }
  
  getFormattedCost() {
    return `$${this.costs.total.toFixed(4)}`;
  }
  
  getCostBreakdown() {
    return {
      "Background Removal": `$${this.costs.backgroundRemoval.toFixed(4)}`,
      "Image Enhancement": `$${this.costs.imageUpscaling.toFixed(4)}`,
      "Background Generation": `$${this.costs.backgroundGeneration.toFixed(4)}`,
      "Total": this.getFormattedCost()
    };
  }
}
```

### **9. OPTIMIZATION STRATEGIES**

#### **Model Selection Matrix:**
| Task | Fast/Cheap | Balanced | High Quality |
|------|------------|----------|--------------|
| Background Removal | runware:109@1 | runware:112@5 | runware:112@9 |
| Image Generation | SD 1.5 | runware:100@1 | SDXL + Refiner |
| Upscaling | 2x | 2x | 4x |

#### **Performance Optimization:**
```javascript
const OPTIMIZATION_CONFIGS = {
  fast: {
    backgroundModel: "runware:109@1",
    upscaleFactor: 2,
    generateCount: 2,
    finalPolish: false,
    estimatedCost: 0.02
  },
  balanced: {
    backgroundModel: "runware:112@5", 
    upscaleFactor: 2,
    generateCount: 4,
    finalPolish: false,
    estimatedCost: 0.035
  },
  premium: {
    backgroundModel: "runware:112@9",
    upscaleFactor: 4, 
    generateCount: 6,
    finalPolish: true,
    estimatedCost: 0.08
  }
};
```

### **10. CRITICAL IMPLEMENTATION CHECKPOINTS**

#### **Pre-Processing Validation:**
- [ ] File type validation complete
- [ ] File size within limits
- [ ] URL accessibility confirmed  
- [ ] API key validated
- [ ] Input format correctly prepared

#### **During Processing:**
- [ ] UUID generation for each task
- [ ] Proper task sequencing maintained
- [ ] Error handling active for each step
- [ ] Progress tracking functional
- [ ] Cost accumulation working

#### **Post-Processing Verification:**
- [ ] All URLs accessible
- [ ] Image quality acceptable
- [ ] Metadata correctly stored
- [ ] Download functionality tested
- [ ] Cost calculation accurate

#### **Error Recovery Testing:**
- [ ] Network failure handling
- [ ] API timeout recovery
- [ ] Invalid response processing
- [ ] Rate limit compliance
- [ ] Graceful degradation active

### **11. PRODUCTION DEPLOYMENT REQUIREMENTS**

#### **Environment Configuration:**
```env
RUNWARE_API_KEY=<ACTUAL-KEY>
MAX_FILE_SIZE=10485760
ALLOWED_FORMATS=jpg,jpeg,png,webp
ENABLE_LOCAL_STORAGE=false
COST_LIMIT_PER_JOB=1.00
RATE_LIMIT_PER_MINUTE=60
AUTO_CLEANUP_HOURS=24
```

#### **Security Measures:**
- API key validation before any processing
- File type validation both client and server side
- URL sanitization for external image sources
- Rate limiting per IP/user
- Cost limits per session/user
- Input sanitization for all text fields

#### **Monitoring & Logging:**
- API response times
- Success/failure rates by task type
- Cost per job tracking
- Error frequency analysis
- User behavior patterns
- Resource utilization metrics

---

**This scratchpad provides the complete logical framework for implementing a production-ready Runware product photography system. Follow each section sequentially, validate at each checkpoint, and implement comprehensive error handling throughout the entire pipeline.**