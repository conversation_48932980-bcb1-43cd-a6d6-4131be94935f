# 🎨 Runware Product Photography

Website xử lý ảnh sản phẩm tự động sử dụng Runware AI API. Tự động xóa background, nâng cấp chất lượng và tạo background mới cho ảnh sản phẩm.

## ✨ Tính năng

- **🎯 Xóa Background tự động** - Sử dụng BiRefNet General model để xóa background chính xác
- **✨ Nâng cấp chất lượng** - Upscale ảnh với độ phân giải cao hơn (2x, 3x, 4x)
- **🎨 Tạo Background mới** - Tạo nhiều background variants với các phong cách khác nhau
- **📊 Theo dõi tiến trình** - Progress bar và cost tracking chi tiết
- **📥 Tải xuống dễ dàng** - Download từng ảnh hoặc tất cả cùng lúc
- **📱 Responsive Design** - Hoạt động tốt trên mọi thiết bị

## 🚀 Cách sử dụng

### 1. Mở website
Mở file `index.html` trong trình duyệt web.

### 2. Tải ảnh lên
- **Cách 1**: Kéo thả ảnh vào vùng upload
- **Cách 2**: Click vào vùng upload để chọn file
- **Cách 3**: Nhập URL ảnh (HTTPS only)

### 3. Cài đặt tùy chọn
- **Phong cách background**: Chọn từ 6 phong cách có sẵn
- **Độ phóng to**: 2x (khuyến nghị), 3x, hoặc 4x
- **Số lượng background**: 2, 4, hoặc 6 variants

### 4. Bắt đầu xử lý
Click nút "🚀 Bắt đầu xử lý" và chờ kết quả.

### 5. Tải xuống kết quả
- Click vào ảnh để xem full size
- Click nút "📥 Tải xuống" để download từng ảnh
- Click "📦 Tải xuống tất cả" để download toàn bộ

## 🎨 Phong cách Background

1. **Tối giản (Minimalist)** - Background trắng sạch, ánh sáng mềm
2. **Sang trọng (Luxury)** - Bề mặt marble, ánh sáng elegant
3. **Tự nhiên (Natural)** - Bàn gỗ tự nhiên, ánh sáng ấm
4. **Hiện đại (Modern)** - Bề mặt beton, ánh sáng contemporary
5. **Lifestyle** - Không gian gia đình ấm cúng
6. **Studio** - Studio chuyên nghiệp với softbox lighting

## ⚙️ Cấu hình API

API key Runware đã được cấu hình sẵn trong file `script.js`:

```javascript
const RUNWARE_API_KEY = 'yRQ2DLBEKBBPemhULO4sLXLUy4VwITMJ';
```

## 🔧 Cấu trúc dự án

```
product-background/
├── index.html          # Giao diện chính
├── style.css           # Styling và responsive design
├── script.js           # Logic xử lý API và UI
├── README.md           # Hướng dẫn sử dụng
└── scratchpad.md       # Tài liệu kỹ thuật chi tiết
```

## 🛠️ Công nghệ sử dụng

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **API**: Runware AI API
- **Models**: 
  - BiRefNet General (Background Removal)
  - SDXL (Image Generation)
  - SDXL Canny ControlNet (Structure Control)

## 📋 Quy trình xử lý

### Bước 1: Xóa Background
- Model: `runware:112@5` (BiRefNet General)
- Output: PNG với transparency
- Alpha matting để làm mịn edges

### Bước 2: Nâng cấp chất lượng
- Upscale factor: 2x, 3x, hoặc 4x
- Giữ nguyên transparency
- Tối đa 4096x4096 pixels

### Bước 3: Tạo Background mới
- Model: `runware:100@1` (SDXL)
- ControlNet: `runware:20@1` (Canny)
- Tạo nhiều variants với prompt engineering

## 💰 Chi phí ước tính

- **Background Removal**: ~$0.006 per image
- **Upscaling**: ~$0.003 per image
- **Background Generation**: ~$0.008 per variant
- **Tổng cộng**: ~$0.017 - $0.065 tùy số lượng variants

## 🎯 Yêu cầu ảnh đầu vào

- **Định dạng**: JPG, PNG, WEBP
- **Kích thước**: Tối đa 10MB
- **URL**: Phải sử dụng HTTPS
- **Chất lượng**: Càng rõ nét càng tốt

## ⌨️ Phím tắt

- **Ctrl/Cmd + U**: Mở dialog chọn file
- **Enter**: Bắt đầu xử lý (khi đã có ảnh)
- **Escape**: Reset và thử lại

## 🔄 Xử lý lỗi

Website có hệ thống xử lý lỗi tự động:
- **Retry mechanism**: Thử lại tối đa 3 lần với exponential backoff
- **Validation**: Kiểm tra file type, size, URL format
- **Error display**: Hiển thị lỗi chi tiết và hướng dẫn khắc phục

## 📱 Responsive Design

Website được tối ưu cho:
- **Desktop**: Full features
- **Tablet**: Responsive grid layout
- **Mobile**: Single column layout, touch-friendly

## 🚀 Chạy local

1. Clone hoặc download project
2. Mở `index.html` trong trình duyệt
3. Không cần server, chạy trực tiếp từ file system

## 🔒 Bảo mật

- API key được hardcode (chỉ dùng cho demo)
- Không lưu trữ ảnh trên server
- Tất cả xử lý qua Runware API
- URLs ảnh có thời hạn 24-48h

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra console browser (F12)
2. Đảm bảo internet connection ổn định
3. Thử refresh page và upload lại ảnh
4. Kiểm tra format và size ảnh

## 📄 License

Dự án demo sử dụng Runware API. Vui lòng tham khảo [Runware Terms of Service](https://runware.ai/terms) cho thông tin chi tiết về license.

---

**Powered by [Runware AI](https://runware.ai) | Made with ❤️ for product photography**
